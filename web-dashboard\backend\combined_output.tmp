=== TEST RESULTS ===
?   	web-dashboard/backend/config	[no test files]
?   	web-dashboard/backend/debug	[no test files]
?   	web-dashboard/backend/session	[no test files]
?   	web-dashboard/backend/system_settings	[no test files]
?   	web-dashboard/backend/users	[no test files]
?   	web-dashboard/backend/security	[no test files]
?   	web-dashboard/backend/testutils	[no test files]
?   	web-dashboard/backend/email	[no test files]
=== RUN   TestBasicFunctionality
=== RUN   TestBasicFunctionality/Environment_Setup
    main_test.go:31: ✅ Test environment properly configured
--- PASS: TestBasicFunctionality (0.00s)
    --- PASS: TestBasicFunctionality/Environment_Setup (0.00s)
PASS
ok  	web-dashboard/backend	1.158s
=== RUN   TestCheckSessionPermission
=== RUN   TestCheckSessionPermission/nil_session_data
=== RUN   TestCheckSessionPermission/nil_permissions
=== RUN   TestCheckSessionPermission/wildcard_permission
=== RUN   TestCheckSessionPermission/specific_permission_match
=== RUN   TestCheckSessionPermission/no_permission_match
--- PASS: TestCheckSessionPermission (0.00s)
    --- PASS: TestCheckSessionPermission/nil_session_data (0.00s)
    --- PASS: TestCheckSessionPermission/nil_permissions (0.00s)
    --- PASS: TestCheckSessionPermission/wildcard_permission (0.00s)
    --- PASS: TestCheckSessionPermission/specific_permission_match (0.00s)
    --- PASS: TestCheckSessionPermission/no_permission_match (0.00s)
=== RUN   TestRequirePermissions
=== RUN   TestRequirePermissions/valid_permissions
=== RUN   TestRequirePermissions/invalid_permissions
--- PASS: TestRequirePermissions (0.00s)
    --- PASS: TestRequirePermissions/valid_permissions (0.00s)
    --- PASS: TestRequirePermissions/invalid_permissions (0.00s)
=== RUN   TestPermissionsRoutes
=== RUN   TestPermissionsRoutes/GET_/permission/permissions_without_auth_returns_401
--- PASS: TestPermissionsRoutes (0.00s)
    --- PASS: TestPermissionsRoutes/GET_/permission/permissions_without_auth_returns_401 (0.00s)
=== RUN   TestRedisRoutes
=== RUN   TestRedisRoutes/GET_/redis/stats_without_auth_returns_401
--- PASS: TestRedisRoutes (0.00s)
    --- PASS: TestRedisRoutes/GET_/redis/stats_without_auth_returns_401 (0.00s)
=== RUN   TestSecurityGroupsRoutes
=== RUN   TestSecurityGroupsRoutes/GET_/security-groups_without_auth_returns_401
--- PASS: TestSecurityGroupsRoutes (0.00s)
    --- PASS: TestSecurityGroupsRoutes/GET_/security-groups_without_auth_returns_401 (0.00s)
=== RUN   TestSystemSettingsRoutes
=== RUN   TestSystemSettingsRoutes/GET_/system-settings/users_without_auth_returns_401
--- PASS: TestSystemSettingsRoutes (0.00s)
    --- PASS: TestSystemSettingsRoutes/GET_/system-settings/users_without_auth_returns_401 (0.00s)
=== RUN   TestUsersRoutes
=== RUN   TestUsersRoutes/GET_/user/users_without_auth_returns_401
--- PASS: TestUsersRoutes (0.00s)
    --- PASS: TestUsersRoutes/GET_/user/users_without_auth_returns_401 (0.00s)
PASS
ok  	web-dashboard/backend/api	1.172s
=== RUN   TestGetBaseURL
=== RUN   TestGetBaseURL/fallback_to_request_host_with_TLS
=== RUN   TestGetBaseURL/fallback_to_request_host_without_TLS
--- PASS: TestGetBaseURL (0.07s)
    --- PASS: TestGetBaseURL/fallback_to_request_host_with_TLS (0.03s)
    --- PASS: TestGetBaseURL/fallback_to_request_host_without_TLS (0.03s)
=== RUN   TestAuthHandler
=== RUN   TestAuthHandler/GET_auth_page_in_test_mode
=== RUN   TestAuthHandler/POST_with_unsupported_provider
=== RUN   TestAuthHandler/PUT_method_not_allowed
--- PASS: TestAuthHandler (0.00s)
    --- PASS: TestAuthHandler/GET_auth_page_in_test_mode (0.00s)
    --- PASS: TestAuthHandler/POST_with_unsupported_provider (0.00s)
    --- PASS: TestAuthHandler/PUT_method_not_allowed (0.00s)
=== RUN   TestGetCurrentUserEmail
=== RUN   TestGetCurrentUserEmail/no_session_token
=== RUN   TestGetCurrentUserEmail/invalid_session_token
--- PASS: TestGetCurrentUserEmail (0.00s)
    --- PASS: TestGetCurrentUserEmail/no_session_token (0.00s)
    --- PASS: TestGetCurrentUserEmail/invalid_session_token (0.00s)
=== RUN   TestUserInfoHandler
=== RUN   TestUserInfoHandler/no_session_returns_unauthorized
--- PASS: TestUserInfoHandler (0.00s)
    --- PASS: TestUserInfoHandler/no_session_returns_unauthorized (0.00s)
=== RUN   TestLogoutHandler
=== RUN   TestLogoutHandler/logout_without_session
--- PASS: TestLogoutHandler (0.00s)
    --- PASS: TestLogoutHandler/logout_without_session (0.00s)
=== RUN   TestAuthMiddleware
=== RUN   TestAuthMiddleware/unauthenticated_API_request
=== RUN   TestAuthMiddleware/unauthenticated_web_request
--- PASS: TestAuthMiddleware (0.00s)
    --- PASS: TestAuthMiddleware/unauthenticated_API_request (0.00s)
    --- PASS: TestAuthMiddleware/unauthenticated_web_request (0.00s)
=== RUN   TestInitGoogleOAuth
--- PASS: TestInitGoogleOAuth (0.04s)
=== RUN   TestHandleGoogleOAuth
=== RUN   TestHandleGoogleOAuth/valid_OAuth_config_redirects_to_Google
=== RUN   TestHandleGoogleOAuth/missing_client_ID_returns_error
=== RUN   TestHandleGoogleOAuth/missing_client_secret_returns_error
--- PASS: TestHandleGoogleOAuth (0.09s)
    --- PASS: TestHandleGoogleOAuth/valid_OAuth_config_redirects_to_Google (0.03s)
    --- PASS: TestHandleGoogleOAuth/missing_client_ID_returns_error (0.03s)
    --- PASS: TestHandleGoogleOAuth/missing_client_secret_returns_error (0.03s)
=== RUN   TestGoogleCallbackHandler
=== RUN   TestGoogleCallbackHandler/missing_state_parameter
2025/07/25 16:01:09 [ERROR] Invalid state parameter: 
=== RUN   TestGoogleCallbackHandler/invalid_state_parameter
2025/07/25 16:01:09 [ERROR] Invalid state parameter: invalid
=== RUN   TestGoogleCallbackHandler/missing_code_parameter
2025/07/25 16:01:09 [WARN] GoogleCallbackHandler - Missing authorization code
=== RUN   TestGoogleCallbackHandler/valid_state_but_no_actual_OAuth_flow
2025/07/25 16:01:09 [ERROR] GoogleCallbackHandler - Failed to exchange token: oauth2: "invalid_client" "The OAuth client was not found."
--- PASS: TestGoogleCallbackHandler (0.21s)
    --- PASS: TestGoogleCallbackHandler/missing_state_parameter (0.00s)
    --- PASS: TestGoogleCallbackHandler/invalid_state_parameter (0.00s)
    --- PASS: TestGoogleCallbackHandler/missing_code_parameter (0.00s)
    --- PASS: TestGoogleCallbackHandler/valid_state_but_no_actual_OAuth_flow (0.17s)
=== RUN   TestCreateOrGetGoogleUser
=== RUN   TestCreateOrGetGoogleUser/create_new_Google_user
2025/07/25 16:01:09 [INFO] Successfully added user 270 to security group 2
=== RUN   TestCreateOrGetGoogleUser/get_existing_Google_user
2025/07/25 16:01:09 [INFO] Successfully added user 271 to security group 2
=== RUN   TestCreateOrGetGoogleUser/invalid_email_format
--- PASS: TestCreateOrGetGoogleUser (0.54s)
    --- PASS: TestCreateOrGetGoogleUser/create_new_Google_user (0.23s)
    --- PASS: TestCreateOrGetGoogleUser/get_existing_Google_user (0.32s)
    --- PASS: TestCreateOrGetGoogleUser/invalid_email_format (0.00s)
=== RUN   TestGoogleOAuthWithSystemSettings
--- PASS: TestGoogleOAuthWithSystemSettings (0.03s)
=== RUN   TestGoogleOAuthErrorHandling
=== RUN   TestGoogleOAuthErrorHandling/OAuth_not_configured
=== RUN   TestGoogleOAuthErrorHandling/Partial_OAuth_configuration
--- PASS: TestGoogleOAuthErrorHandling (0.06s)
    --- PASS: TestGoogleOAuthErrorHandling/OAuth_not_configured (0.03s)
    --- PASS: TestGoogleOAuthErrorHandling/Partial_OAuth_configuration (0.03s)
=== RUN   TestGoogleCallbackStateValidation
=== RUN   TestGoogleCallbackStateValidation/valid_state
2025/07/25 16:01:09 [ERROR] GoogleCallbackHandler - Failed to exchange token: oauth2: "invalid_client" "The OAuth client was not found."
=== RUN   TestGoogleCallbackStateValidation/invalid_state
2025/07/25 16:01:09 [ERROR] Invalid state parameter: invalid
=== RUN   TestGoogleCallbackStateValidation/empty_state
2025/07/25 16:01:09 [ERROR] Invalid state parameter: 
--- PASS: TestGoogleCallbackStateValidation (0.09s)
    --- PASS: TestGoogleCallbackStateValidation/valid_state (0.06s)
    --- PASS: TestGoogleCallbackStateValidation/invalid_state (0.00s)
    --- PASS: TestGoogleCallbackStateValidation/empty_state (0.00s)
=== RUN   TestHashPassword
=== RUN   TestHashPassword/simple_password
=== RUN   TestHashPassword/complex_password
=== RUN   TestHashPassword/empty_password
--- PASS: TestHashPassword (0.35s)
    --- PASS: TestHashPassword/simple_password (0.14s)
    --- PASS: TestHashPassword/complex_password (0.13s)
    --- PASS: TestHashPassword/empty_password (0.08s)
=== RUN   TestVerifyPassword
=== RUN   TestVerifyPassword/correct_password
=== RUN   TestVerifyPassword/incorrect_password
=== RUN   TestVerifyPassword/invalid_hash_format
=== RUN   TestVerifyPassword/empty_password
=== RUN   TestVerifyPassword/empty_hash
--- PASS: TestVerifyPassword (0.27s)
    --- PASS: TestVerifyPassword/correct_password (0.05s)
    --- PASS: TestVerifyPassword/incorrect_password (0.13s)
    --- PASS: TestVerifyPassword/invalid_hash_format (0.00s)
    --- PASS: TestVerifyPassword/empty_password (0.05s)
    --- PASS: TestVerifyPassword/empty_hash (0.00s)
=== RUN   TestLocalLoginHandler
2025/07/25 16:01:10 [WARN] Redis connection attempt 1 failed, retrying...
2025/07/25 16:01:11 [WARN] Redis connection attempt 2 failed, retrying...
    local_test.go:114: Redis not available for testing, skipping test
--- SKIP: TestLocalLoginHandler (3.29s)
=== RUN   TestChangePasswordHandler
2025/07/25 16:01:13 [WARN] Redis connection attempt 1 failed, retrying...
2025/07/25 16:01:15 [WARN] Redis connection attempt 2 failed, retrying...
    local_test.go:224: Redis not available for testing, skipping test
--- SKIP: TestChangePasswordHandler (3.26s)
=== RUN   TestGenerateSecureToken
--- PASS: TestGenerateSecureToken (0.00s)
=== RUN   TestGetClientIP
=== RUN   TestGetClientIP/X-Forwarded-For_header_single_IP
=== RUN   TestGetClientIP/X-Forwarded-For_header_multiple_IPs
=== RUN   TestGetClientIP/X-Real-IP_header
=== RUN   TestGetClientIP/RemoteAddr_fallback
=== RUN   TestGetClientIP/RemoteAddr_without_port
--- PASS: TestGetClientIP (0.00s)
    --- PASS: TestGetClientIP/X-Forwarded-For_header_single_IP (0.00s)
    --- PASS: TestGetClientIP/X-Forwarded-For_header_multiple_IPs (0.00s)
    --- PASS: TestGetClientIP/X-Real-IP_header (0.00s)
    --- PASS: TestGetClientIP/RemoteAddr_fallback (0.00s)
    --- PASS: TestGetClientIP/RemoteAddr_without_port (0.00s)
=== RUN   TestBruteForceProtection
2025/07/25 16:01:17 [WARN] SECURITY: IP ************* blocked for 5 minutes after 3 reset token attempts
--- PASS: TestBruteForceProtection (0.00s)
=== RUN   TestGetUserLoginInfo
=== RUN   TestGetUserLoginInfo/valid_user_email
DEBUG: GetUserLoginInfo called for email: <EMAIL>
DEBUG: Session manager is nil, cannot get IP history
DEBUG: Final userInfo IP history: []
DEBUG: GetUserLoginInfo returning <NAME_EMAIL> with 0 IP records
DEBUG: IP History JSON: []
=== RUN   TestGetUserLoginInfo/invalid_user_email
DEBUG: GetUserLoginInfo called for email: <EMAIL>
DEBUG: GetUserByEmail <NAME_EMAIL>: user not found
=== RUN   TestGetUserLoginInfo/empty_email
DEBUG: GetUserLoginInfo called for email: 
DEBUG: GetUserLoginInfo called with empty email
--- PASS: TestGetUserLoginInfo (0.27s)
    --- PASS: TestGetUserLoginInfo/valid_user_email (0.03s)
    --- PASS: TestGetUserLoginInfo/invalid_user_email (0.03s)
    --- PASS: TestGetUserLoginInfo/empty_email (0.00s)
=== RUN   TestGenerateSessionID
--- PASS: TestGenerateSessionID (0.00s)
=== RUN   TestCreateSession
    sessions_test.go:33: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/auth/sessions_test.go:33
        	Error:      	Received unexpected error:
        	            	user <NAME_EMAIL> already exists
        	Test:       	TestCreateSession
--- FAIL: TestCreateSession (0.09s)
=== RUN   TestGetSession
    sessions_test.go:100: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/auth/sessions_test.go:100
        	Error:      	Received unexpected error:
        	            	ERROR: relation "sessions" does not exist (SQLSTATE 42P01)
        	Test:       	TestGetSession
--- FAIL: TestGetSession (0.17s)
=== RUN   TestInvalidateSession
    sessions_test.go:158: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/auth/sessions_test.go:158
        	Error:      	Received unexpected error:
        	            	ERROR: relation "sessions" does not exist (SQLSTATE 42P01)
        	Test:       	TestInvalidateSession
--- FAIL: TestInvalidateSession (0.18s)
=== RUN   TestInvalidateAllUserSessions
    sessions_test.go:213: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/auth/sessions_test.go:213
        	Error:      	Received unexpected error:
        	            	user <NAME_EMAIL> already exists
        	Test:       	TestInvalidateAllUserSessions
--- FAIL: TestInvalidateAllUserSessions (0.09s)
=== RUN   TestCleanupExpiredSessions
    sessions_test.go:279: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/auth/sessions_test.go:279
        	Error:      	Received unexpected error:
        	            	user <NAME_EMAIL> already exists
        	Test:       	TestCleanupExpiredSessions
--- FAIL: TestCleanupExpiredSessions (0.08s)
=== RUN   TestSessionLifecycle
    sessions_test.go:346: 
        	Error Trace:	C:/Go Projects/web-dashboard/web-dashboard/backend/auth/sessions_test.go:346
        	Error:      	Received unexpected error:
        	            	ERROR: relation "sessions" does not exist (SQLSTATE 42P01)
        	Test:       	TestSessionLifecycle
--- FAIL: TestSessionLifecycle (0.17s)
=== RUN   TestGenerateTOTPSecret
=== RUN   TestGenerateTOTPSecret/valid_email
=== RUN   TestGenerateTOTPSecret/email_with_special_characters
--- PASS: TestGenerateTOTPSecret (0.07s)
    --- PASS: TestGenerateTOTPSecret/valid_email (0.04s)
    --- PASS: TestGenerateTOTPSecret/email_with_special_characters (0.03s)
=== RUN   TestStoreTOTPSecret
=== RUN   TestStoreTOTPSecret/valid_secret_storage
=== RUN   TestStoreTOTPSecret/update_existing_secret
=== RUN   TestStoreTOTPSecret/invalid_user_ID
--- PASS: TestStoreTOTPSecret (0.34s)
    --- PASS: TestStoreTOTPSecret/valid_secret_storage (0.06s)
    --- PASS: TestStoreTOTPSecret/update_existing_secret (0.07s)
    --- PASS: TestStoreTOTPSecret/invalid_user_ID (0.03s)
=== RUN   TestGetTOTPSecret
=== RUN   TestGetTOTPSecret/valid_user_with_TOTP_secret
=== RUN   TestGetTOTPSecret/user_without_TOTP_secret
--- PASS: TestGetTOTPSecret (0.29s)
    --- PASS: TestGetTOTPSecret/valid_user_with_TOTP_secret (0.03s)
    --- PASS: TestGetTOTPSecret/user_without_TOTP_secret (0.03s)
=== RUN   TestEnableTOTP
=== RUN   TestEnableTOTP/enable_TOTP_for_valid_user
=== RUN   TestEnableTOTP/enable_TOTP_for_user_without_secret
--- PASS: TestEnableTOTP (0.34s)
    --- PASS: TestEnableTOTP/enable_TOTP_for_valid_user (0.07s)
    --- PASS: TestEnableTOTP/enable_TOTP_for_user_without_secret (0.03s)
=== RUN   TestDisableTOTP
=== RUN   TestDisableTOTP/disable_TOTP_for_valid_user
=== RUN   TestDisableTOTP/disable_TOTP_for_user_without_secret
--- PASS: TestDisableTOTP (0.36s)
    --- PASS: TestDisableTOTP/disable_TOTP_for_valid_user (0.07s)
    --- PASS: TestDisableTOTP/disable_TOTP_for_user_without_secret (0.03s)
=== RUN   TestValidateTOTP
=== RUN   TestValidateTOTP/valid_TOTP_code
=== RUN   TestValidateTOTP/invalid_TOTP_code
=== RUN   TestValidateTOTP/user_without_TOTP
--- PASS: TestValidateTOTP (0.36s)
    --- PASS: TestValidateTOTP/valid_TOTP_code (0.03s)
    --- PASS: TestValidateTOTP/invalid_TOTP_code (0.03s)
    --- PASS: TestValidateTOTP/user_without_TOTP (0.04s)
=== RUN   TestRequireTOTP
=== RUN   TestRequireTOTP/user_without_TOTP
=== RUN   TestRequireTOTP/user_with_enabled_TOTP
=== RUN   TestRequireTOTP/nonexistent_user
--- PASS: TestRequireTOTP (0.36s)
    --- PASS: TestRequireTOTP/user_without_TOTP (0.04s)
    --- PASS: TestRequireTOTP/user_with_enabled_TOTP (0.13s)
    --- PASS: TestRequireTOTP/nonexistent_user (0.03s)
=== RUN   TestSetupTOTPHandler
--- FAIL: TestSetupTOTPHandler (0.15s)
panic: runtime error: invalid memory address or nil pointer dereference [recovered]
	panic: runtime error: invalid memory address or nil pointer dereference
[signal 0xc0000005 code=0x0 addr=0x0 pc=0x1409eb1a6]

goroutine 743 [running]:
testing.tRunner.func1.2({0x141140a60, 0x141033d40})
	C:/Users/<USER>/sdk/go1.23.9/src/testing/testing.go:1632 +0x3df
testing.tRunner.func1()
	C:/Users/<USER>/sdk/go1.23.9/src/testing/testing.go:1635 +0x6b6
panic({0x141140a60?, 0x141033d40?})
	C:/Users/<USER>/sdk/go1.23.9/src/runtime/panic.go:791 +0x132
web-dashboard/backend/session.(*RedisSessionManager).CreateSession(0x0, {0x14129f3cf, 0x12}, 0x11a, {0x1412a268b, 0x15}, {0x141291a19, 0x4}, {0xc00004e510, 0x1, ...}, ...)
	C:/Go Projects/web-dashboard/web-dashboard/backend/session/redis.go:212 +0x306
web-dashboard/backend/auth.TestSetupTOTPHandler(0xc0003789c0)
	C:/Go Projects/web-dashboard/web-dashboard/backend/auth/totp_test.go:397 +0x1dc
testing.tRunner(0xc0003789c0, 0x1412f2e38)
	C:/Users/<USER>/sdk/go1.23.9/src/testing/testing.go:1690 +0x1de
created by testing.(*T).Run in goroutine 1
	C:/Users/<USER>/sdk/go1.23.9/src/testing/testing.go:1743 +0x83d
FAIL	web-dashboard/backend/auth	12.311s
=== RUN   TestInitDB
--- PASS: TestInitDB (0.43s)
=== RUN   TestGetRecords
--- PASS: TestGetRecords (0.03s)
=== RUN   TestSystemSettings
=== RUN   TestSystemSettings/SetAndGetSystemSetting
=== RUN   TestSystemSettings/GetNonExistentSetting
=== RUN   TestSystemSettings/GetAllSystemSettings
--- PASS: TestSystemSettings (0.22s)
    --- PASS: TestSystemSettings/SetAndGetSystemSetting (0.12s)
    --- PASS: TestSystemSettings/GetNonExistentSetting (0.07s)
    --- PASS: TestSystemSettings/GetAllSystemSettings (0.03s)
=== RUN   TestRebuildSchema
--- PASS: TestRebuildSchema (0.07s)
PASS
ok  	web-dashboard/backend/database	2.522s
FAIL
