package auth

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"web-dashboard/backend/database"
	"web-dashboard/backend/testutils"

	"github.com/gorilla/sessions"
	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	// Set up test environment using shared utilities
	testutils.SetupTestEnvironment()

	// Initialize database FIRST before auth
	err := database.InitDB()
	if err != nil {
		fmt.Printf("Failed to initialize database for auth tests: %v\n", err)
		os.Exit(1)
	}

	// Initialize auth for testing
	sessionKey := "test-session-key-32-bytes-long-123"
	store := sessions.NewCookieStore([]byte(sessionKey))
	store.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   3600,
		HttpOnly: true,
		Secure:   false,
		SameSite: http.SameSiteLaxMode,
	}

	// Initialize auth system (now that database is ready)
	Init(store)

	// Run tests
	code := m.Run()

	// Cleanup
	if database.DB != nil {
		database.DB.Close()
	}

	os.Exit(code)
}

func TestGetBaseURL(t *testing.T) {
	tests := []struct {
		name        string
		siteURL     string
		requestHost string
		requestTLS  bool
		expected    string
	}{
		{
			name:        "fallback to request host with TLS",
			siteURL:     "",
			requestHost: "example.com",
			requestTLS:  true,
			expected:    "https://example.com",
		},
		{
			name:        "fallback to request host without TLS",
			siteURL:     "",
			requestHost: "localhost:8080",
			requestTLS:  false,
			expected:    "http://localhost:8080",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test request
			req := httptest.NewRequest("GET", "/", nil)
			req.Host = tt.requestHost
			if tt.requestTLS {
				req.TLS = &tls.ConnectionState{}
			}

			result := GetBaseURL(req)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAuthHandler(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		queryParams    string
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "GET auth page in test mode",
			method:         "GET",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			expectedBody:   "Auth Page",
		},
		{
			name:           "POST with unsupported provider",
			method:         "POST",
			queryParams:    "",
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name:           "PUT method not allowed",
			method:         "PUT",
			queryParams:    "",
			expectedStatus: http.StatusMethodNotAllowed,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, "/auth"+tt.queryParams, nil)
			rr := httptest.NewRecorder()

			AuthHandler(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)
			if tt.expectedBody != "" {
				assert.Contains(t, rr.Body.String(), tt.expectedBody)
			}
		})
	}
}

func TestGetCurrentUserEmail(t *testing.T) {
	tests := []struct {
		name        string
		expectError bool
	}{
		{
			name:        "no session token",
			expectError: true,
		},
		{
			name:        "invalid session token",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/", nil)

			_, err := GetCurrentUserEmail(req)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestUserInfoHandler(t *testing.T) {
	tests := []struct {
		name           string
		expectedStatus int
	}{
		{
			name:           "no session returns unauthorized",
			expectedStatus: http.StatusUnauthorized,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/user/info", nil)
			rr := httptest.NewRecorder()
			UserInfoHandler(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

func TestLogoutHandler(t *testing.T) {
	tests := []struct {
		name           string
		expectedStatus int
		expectRedirect bool
	}{
		{
			name:           "logout without session",
			expectedStatus: http.StatusFound,
			expectRedirect: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/logout", nil)
			rr := httptest.NewRecorder()
			LogoutHandler(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.expectRedirect {
				location := rr.Header().Get("Location")
				assert.Equal(t, "/auth", location)
			}

			// Check that session cookie is cleared
			cookies := rr.Result().Cookies()
			for _, cookie := range cookies {
				if cookie.Name == "session_token" {
					assert.Equal(t, "", cookie.Value)
					assert.Equal(t, -1, cookie.MaxAge)
				}
			}
		})
	}
}

func TestAuthMiddleware(t *testing.T) {
	// Create a test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("authenticated"))
	})

	middleware := AuthMiddleware(testHandler)

	tests := []struct {
		name           string
		path           string
		isAPIRequest   bool
		expectedStatus int
		expectRedirect bool
	}{
		{
			name:           "unauthenticated API request",
			path:           "/api/test",
			isAPIRequest:   true,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "unauthenticated web request",
			path:           "/dashboard",
			isAPIRequest:   false,
			expectedStatus: http.StatusFound,
			expectRedirect: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", tt.path, nil)
			if tt.isAPIRequest {
				req.Header.Set("Accept", "application/json")
			}

			rr := httptest.NewRecorder()
			middleware.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.expectRedirect {
				location := rr.Header().Get("Location")
				assert.Equal(t, "/auth", location)
			}
		})
	}
}
